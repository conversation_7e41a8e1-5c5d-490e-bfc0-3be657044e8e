image: node:18

pipelines:
  branches:
    development:
      - step:
          name: Build and Deploy Frontend to S3
          image: node:18
          caches:
            - node
          script:
            - echo "Installing dependencies..."
            - npm install
            - echo "Building the project..."
            - npm run build
            - echo "Installing AWS CLI..."
            - apt-get update && apt-get install -y awscli
            - echo "Deploying to S3..."
            - aws s3 rm s3://poc.flashana.com/ --recursive --region us-east-1
            - aws s3 cp dist/ s3://poc.flashana.com/ --recursive --region us-east-1
          artifacts:
            - dist/**
