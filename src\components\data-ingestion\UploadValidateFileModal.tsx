import { useState, useRef, useEffect } from "react";
import {
  X,
  Upload,
  CheckCircle,
  AlertCircle,
  Trash2,
  CloudUploadIcon,
} from "lucide-react";
import {
  uploadPreSignedUrl,
  useUploadCorrectedFile,
} from "@/hooks/validateFileUploads/validateFileUploads";
import { useToast } from "@/hooks/useToast";

interface UploadFile {
  id: string;
  file: File;
  status: "pending" | "uploading" | "completed" | "error";
  progress: number;
  error?: string;
}

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  batchId: number;
  onValidateComplete: any;
}

export default function UploadModal({
  isOpen,
  onClose,
  batchId,
  onValidateComplete,
}: UploadModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [isValidated, setIsValidated] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toasts, removeToast, success, error } = useToast();
  const [uploadStatusApiCall, setUploadStatusApiCall] = useState(false);

  // ✅ Place useEffect right here
  useEffect(() => {
    if (isOpen) {
      setUploadFiles([]);
      setIsValidated(false);
      setOverallProgress(0);
    }
  }, [isOpen]);

  const allowedFileTypes = [".xlsx", ".xls"];
  const maxFileSize = 120 * 1024 * 1024;

  const validateFile = (file: File): string | null => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!allowedFileTypes.includes(extension)) {
      return `File type ${extension} is not supported. Only .xls and .csv allowed.`;
    }
    if (file.size > maxFileSize) {
      return `File size exceeds 120MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(1)} MB`;
    }
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0]; // only single file allowed
    const error = validateFile(file);

    setUploadFiles([
      {
        id: Date.now().toString(),
        file,
        status: error ? "error" : "pending",
        progress: 0,
        error,
      },
    ]);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
    setIsValidated(false);
  };

  const startUpload = async () => {
    const fileObj = uploadFiles[0];
    if (!fileObj || fileObj.status === "error") return;

    setIsUploading(true);
    setOverallProgress(0);

    const payloadData: any = {
      batch_id: batchId,
      file_name: uploadFiles && uploadFiles[0]?.file?.name,
    };
    console.log("File", uploadFiles && uploadFiles[0]?.file?.name);
    uploadCorrectedFile(payloadData);

    // setUploadFiles((prev) =>
    //   prev.map((f) =>
    //     f.id === fileObj.id ? { ...f, status: "uploading", progress: 0 } : f
    //   )
    // );

    // for (let progress = 0; progress <= 100; progress += 10) {
    //   await new Promise((resolve) => setTimeout(resolve, 100));
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === fileObj.id ? { ...f, progress: progress } : f
    //     )
    //   );
    //   setOverallProgress(progress);
    // }

    // setUploadFiles((prev) =>
    //   prev.map((f) =>
    //     f.id === fileObj.id ? { ...f, status: "completed", progress: 100 } : f
    //   )
    // );

    // setIsUploading(false);
  };

  const handleValidate = () => {
    setUploadFiles([]);
    setIsUploading(false);
    setOverallProgress(0);
    setIsValidated(true);
    onValidateComplete(true);
    onClose(); // close modal after validate
  };

  // FOR CALLING uploadFileUsingPresignedUrl
  //   const onPutSuccessUrl = (data: any) => {
  //   console.log("data", data);
  // };
  // const onPutErrorUrl = (error: any) => {
  //   console.log("error", error);
  // };
  // const { uploadFileUsingPresignedUrl } = uploadPreSignedUrl(
  //   onPutSuccessUrl,
  //   onPutErrorUrl,
  // );
  // const onPostSuccessUrl = (data) => {
  //   // setRequestId(data?.data?.request_id);
  //   const getPresignedUrls = data.data.files;

  //   getPresignedUrls.forEach(async ({ upload_url }, index) => {
  //     const file = dataUploadFile[index];

  //     if (file) {
  //       const uploadFileAndUrl: any = {
  //         upload_url: upload_url,
  //         file: file,
  //       };
  //       await uploadFileUsingPresignedUrl(uploadFileAndUrl);
  //     } else {
  //       console.log(`No corresponding file found for upload_url ${upload_url}`);
  //     }
  //   });
  //   setUploadStatusApiCall(true);
  // };

  const canUpload =
    uploadFiles.length === 1 &&
    uploadFiles[0].status !== "error" &&
    !isUploading;

  const canValidate =
    uploadFiles.length === 1 && uploadFiles[0].status === "completed";

  if (!isOpen) return null;

  const { mutate: uploadCorrectedFile } = useUploadCorrectedFile(
    async (data) => {
      const uploadUrl = data?.result?.data?.upload_url;
      const file = uploadFiles[0];
      console.log("sucess called,,,,", data);

      if (uploadUrl && file) {
        try {
          const uploadFileAndUrl: any = {
            upload_url: uploadUrl,
            file: file,
          };
          await uploadFileUsingPresignedUrl(uploadFileAndUrl);
          setUploadStatusApiCall(true);
          console.log("sucess called,,,,");
        } catch (uploadError) {
          error("Upload Error", "Failed to upload the file. Please try again.");
        }
      } else {
        error(
          "Upload Error",
          `Missing file or upload URL for corrected file upload.`
        );
      }
    },
    (errorRes: any) => {
      setIsLoading(false);
      const errorInfo =
        (errorRes as any)?.error?.message ||
        "Something went wrong. Please try again.";
      error("Upload Error", errorInfo);
    }
  );

  const { mutate: uploadFileUsingPresignedUrl } = uploadPreSignedUrl(
    (data) => {
      setIsLoading(false);
      success("Success", `File uploaded successfully to presigned URL`);
      handleValidate();
      //onUploadComplete(true);
      console.log(":", data);
    },
    (errorRes) => {
      setIsLoading(false);
      error("Error", `Failed to uploaded file on presigned URL: ${errorRes}`);
    }
  );

  return (
    <div
      style={{
        position: "fixed",
        inset: 0,
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
      }}
    >
      <div
        style={{
          width: "600px",
          background: "white",
          borderRadius: "8px",
          padding: "24px",
          boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        }}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: "16px",
          }}
        >
          <h2 style={{ fontSize: "20px", fontWeight: "600" }}>
            Upload Corrected File
          </h2>
          <X onClick={onClose} style={{ cursor: "pointer" }} />
        </div>

        {/* Info Label */}
        <div
          style={{
            backgroundColor: "#e0f2fe",
            border: "1px solid #7dd3fc",
            color: "#0369a1",
            padding: "10px",
            borderRadius: "6px",
            fontSize: "14px",
            marginBottom: "20px",
          }}
        >
          <span>
            ⓘ You can upload only one file at a time, and it must be in XLS
            format.
          </span>
        </div>

        {/* Drag & Drop Area */}
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
          style={{
            border: "2px dashed #e4e4e7",
            borderRadius: "8px",
            backgroundColor: "#fafafa",
            padding: "30px",
            textAlign: "center",
            marginBottom: "20px",
            cursor: isUploading ? "not-allowed" : "pointer",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
            }}
          >
            <CloudUploadIcon size={40} color="#6b7280" />
            <p style={{ fontWeight: 500, marginTop: "10px" }}>
              Drag & Drop file here to upload
            </p>
            <button
              style={{
                marginTop: "10px",
                padding: "6px 16px",
                border: "1px solid #4b5563",
                borderRadius: "4px",
                background: "white",
                cursor: "pointer",
              }}
            >
              Browse Files
            </button>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            accept=".xlsx,.xls"
            onChange={(e) => handleFileSelect(e.target.files)}
            style={{ display: "none" }}
            disabled={isUploading}
          />
        </div>

        {uploadFiles.length > 0 && (
          <div style={{ marginBottom: "1rem" }}>
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b", // black
                marginBottom: "0.75rem",
              }}
            >
              Selected File
            </h3>
          </div>
        )}

        {/* File Display with Progress */}
        {uploadFiles.map((file) => (
          <div
            key={file.id}
            style={{
              background: "#f8fafc",
              border: "1px solid #e2e8f0",
              padding: "10px",
              borderRadius: "6px",
              marginBottom: "10px",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <div style={{ fontWeight: 500 }}>{file.file.name}</div>
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                {file.status === "completed" ? (
                  <CheckCircle size={18} color="#22c55e" />
                ) : file.status === "error" ? (
                  <AlertCircle size={18} color="#ef4444" />
                ) : file.status === "uploading" ? (
                  <span>{file.progress}%</span>
                ) : null}
                {!isUploading && (
                  <button
                    onClick={() => removeFile(file.id)}
                    style={{
                      background: "transparent",
                      border: "none",
                      cursor: "pointer",
                    }}
                  >
                    <Trash2 size={16} color="#ef4444" />
                  </button>
                )}
              </div>
            </div>
            {file.status === "uploading" && (
              <div style={{ marginTop: "6px" }}>
                <div
                  style={{
                    height: "4px",
                    backgroundColor: "#e5e7eb",
                    borderRadius: "2px",
                    overflow: "hidden",
                  }}
                >
                  <div
                    style={{
                      width: `${file.progress}%`,
                      height: "100%",
                      backgroundColor: "#2b524f",
                      transition: "width 0.3s ease",
                    }}
                  ></div>
                </div>
              </div>
            )}
            {file.error && (
              <div
                style={{
                  color: "#dc2626",
                  fontSize: "12px",
                  marginTop: "6px",
                }}
              >
                {file.error}
              </div>
            )}
          </div>
        ))}

        {/* Actions */}
        {/* <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "20px",
          }}
        >
          <button
            onClick={startUpload}
            disabled={!canUpload}
            style={{
              padding: "8px 20px",
              background: canUpload ? "#2b524f" : "#d1d5db",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: canUpload ? "pointer" : "not-allowed",
            }}
          >
            Upload
          </button>
          <button
            onClick={handleValidate}
            disabled={!canValidate}
            style={{
              padding: "8px 20px",
              background: canValidate ? "#1f2937" : "#d1d5db",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: canValidate ? "pointer" : "not-allowed",
            }}
          >
            Validate
          </button>
        </div> */}

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "20px",
          }}
        >
          <button
            onClick={onClose}
            disabled={isUploading}
            style={{
              padding: "8px 20px",
              background: "#f3f4f6",
              color: "#1f2937",
              border: "1px solid #d1d5db",
              borderRadius: "6px",
              cursor: isUploading ? "not-allowed" : "pointer",
              opacity: isUploading ? 0.6 : 1,
            }}
          >
            Cancel
          </button>
          <button
            onClick={startUpload}
            disabled={!canUpload}
            style={{
              padding: "8px 20px",
              background: canUpload ? "#2b524f" : "#d1d5db",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: canUpload ? "pointer" : "not-allowed",
            }}
          >
            {isUploading ? "Uploading..." : "Upload"}
          </button>
        </div>
      </div>
    </div>
  );
}
