export const API_URL = import.meta.env.VITE_API_BASE_URL ? import.meta.env.VITE_API_BASE_URL : "http://35.182.6.172:8000/api/v1";
export const SIGNUP_URL = `${API_URL}/auth/signup`;
export const OTP_VERIFY_URL = `${API_URL}/auth/verify-email`;
export const RESEND_OTP_URL = `${API_URL}/auth/resend-verification-code`;
export const FORGOT_PWD_URL = `${API_URL}/auth/forgot-password`;
export const RESET_PWD_URL = `${API_URL}/auth/confirm-forgot-password`;
export const SIGNIN_URL = `${API_URL}/auth/signin`;
export const GET_DASHBOARD_DATA = `${API_URL}/dashboard`;
export const GETPRESIGNED_URL = `${API_URL}/uploaded-file-batches/upload_batch`;
export const DOWNLOADFILE_URL = `${API_URL}/uploaded-file-batches/download_invalid_records_excel`;
export const GET_STORELIST_URL = `${API_URL}/locations/paginated`;
export const GET_TENANT_URL = `${API_URL}/tenants`; // GET /tenants/{tenant_id}
export const UPDATE_TENANT_URL = `${API_URL}/tenants`; // PATCH /tenants/{tenant_id}
export const GET_BATCH_LIST_URL = `${API_URL}/uploaded-file-batches/list`;
export const GENERATE_FORECAST_URL = `${API_URL}/forecasts/generate_forecast`;
export const FORECAST_PROGRESS_URL = `${API_URL}/forecasts/progress`;
export const FORECAST_RESULTS_URL = `${API_URL}/forecasts/results`;
export const GET_SKU_LIST_URL = `${API_URL}/skus`;
export const GET_FORECAST_ALGORITHMS_URL = `${API_URL}/forecasts/forecast_algorithms`;
export const GET_FORECAST_FACTORS_URL = `${API_URL}/forecasts/forecast_factors`;
export const GETFORECAST_NAME_LIST_URL = `${API_URL}/forecasts/list/dropdown`;
export const GET_FORCAST_LIST_URL = `${API_URL}/forecasts/all`;
export const GET_STORE_TYPES_URL = `${API_URL}/location-types/list`;
export const STORE_URL = `${API_URL}/locations`
export const GET_COMPANY_TYPES_URL = `${API_URL}/industries/list`;
export const IMPORTED_DATA_URL = `${API_URL}/imported-data`;
export const GET_VALIDATION_RESULTS_URL = `${API_URL}/validate-batches`;
export const GET_TABLE_VALIDATION_STATUS_URL  = `${API_URL}/validate-batches/tables_status`;
export const UPLOAD_CORRECTED_FILE = `${API_URL}/uploaded-file-batches/upload_corrected_file`;
export const IMPORT_CORRECTED_DATA = `${API_URL}/uploaded-file-batches/import_corrected_data`;
export const GET_BATCH_AVAILIBILITY_STATUS = `${API_URL}/uploaded-file-batches/batch_upload_status/`;